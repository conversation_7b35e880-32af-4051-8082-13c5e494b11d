import {
  CHARITY_VOTING_OPTIONS,
  GET_CHARITY_RESULT,
  GET_CHARITY_VOTING_CURRENT_RESULTS,
  VOTE_FOR_CHARITY,
} from './ActionTypes';

interface Action {
  type: string;
  payload?: any;
  response?: any;
  responseCallback?: any;
}

export function getCharityVotingOptionsRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: CHARITY_VOTING_OPTIONS.REQUEST,
  };
}

export function voteForCharityRequest(
  payload: any,
  responseCallback: any,
): Action {
  return {
    payload,
    responseCallback,
    type: VOTE_FOR_CHARITY.REQUEST,
  };
}

export function getCharityVotingCurrentResultsRequest(
  responseCallback: any,
): Action {
  return {
    responseCallback,
    type: GET_CHARITY_VOTING_CURRENT_RESULTS.REQUEST,
  };
}

export function getCharityResultRequest(responseCallback: any): Action {
  return {
    responseCallback,
    type: GET_CHARITY_RESULT.REQUEST,
  };
}
