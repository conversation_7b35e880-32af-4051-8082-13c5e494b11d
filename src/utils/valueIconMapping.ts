import {
  LOVE,
  LAUGHTER,
  LIFE,
  RESPECT,
  PURPOSE,
  SAFETY,
} from '../constants/AssetSVGConstants';
import {CHARITY_VOTING} from '../constants/StringConstants';

export type ValueType =
  | 'LOVE'
  | 'LAUGHTER'
  | 'LIFE'
  | 'RESPECT'
  | 'KINDNESS'
  | 'HOPE';

// Map of value types to their corresponding SVG components
export const VALUE_ICON_MAP = {
  LOVE: LOVE,
  LAUGHTER: LAUGHTER,
  LIFE: LIFE,
  RESPECT: RESPECT,
  KINDNESS: PURPOSE, // Using PURPOSE as fallback for KINDNESS
  HOPE: SAFETY, // Using SAFETY as fallback for HOPE
};

// Map of value types to their display names
export const VALUE_NAME_MAP = {
  LOVE: CHARITY_VOTING.VALUES.LOVE,
  LAUGHTER: CHARITY_VOTING.VALUES.LAUGHTER,
  LIFE: CHARITY_VOTING.VALUES.LIFE,
  RESPECT: CHARITY_VOTING.VALUES.RESPECT,
  KINDNESS: CHARITY_VOTING.VALUES.KINDNESS,
  HOPE: CHARITY_VOTING.VALUES.HOPE,
};

// Get the current month's value (for demo purposes, we'll cycle through them)
export const getCurrentMonthValue = (): ValueType => {
  const month = new Date().getMonth(); // 0-11
  const values: ValueType[] = [
    'LOVE',
    'LAUGHTER',
    'LIFE',
    'RESPECT',
    'KINDNESS',
    'HOPE',
  ];
  return values[month % values.length];
};

// Get the previous month's value
export const getPreviousMonthValue = (): ValueType => {
  const month = new Date().getMonth(); // 0-11
  const values: ValueType[] = [
    'LOVE',
    'LAUGHTER',
    'LIFE',
    'RESPECT',
    'KINDNESS',
    'HOPE',
  ];
  const previousMonth = month === 0 ? 11 : month - 1;
  return values[previousMonth % values.length];
};

// Get SVG component for a value type
export const getValueIcon = (valueType: ValueType) => {
  return VALUE_ICON_MAP[valueType];
};

// Get display name for a value type
export const getValueName = (valueType: ValueType): string => {
  return VALUE_NAME_MAP[valueType];
};

// Get current month name
export const getCurrentMonthName = (): string => {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  return months[new Date().getMonth()];
};

// Get next month name
export const getNextMonthName = (): string => {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const nextMonth = (new Date().getMonth() + 1) % 12;
  return months[nextMonth];
};

export const gteCurrentMonth = (): string => {
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];
  const nextMonth = new Date().getMonth() % 12;
  return months[nextMonth];
};
