import React, {useState} from 'react';
import {View, Text, TouchableOpacity, Dimensions} from 'react-native';
import OutsidePressHandler from 'react-native-outside-press';
import {Metrics} from '../../theme';
import Triangle from '../Triangle';
import {NEWS} from '../../constants/StringConstants';
import styles from './styles';

const SCREEN_WIDTH = Dimensions.get('window').width;

const BiasSlider = ({position = 'Center'}: {position: string}) => {
  const [showTooltip, setShowTooltip] = useState(false);

  const positionMap: Record<string, number> = {
    Left: 3,
    'Leans Left': Metrics.screenWidth * 0.045,
    'Lean Left': Metrics.screenWidth * 0.045,
    Center: Metrics.screenWidth * 0.08,
    'Leans Right': 55,
    'Lean Right': 55,
    Right: 74,
  };

  const getDotPosition = () => {
    const margin = 20; // padding left and right of slider bar
    const sliderWidth = SCREEN_WIDTH - margin * 2;
    const pos = positionMap[position] ?? 50;
    return (sliderWidth * pos) / 100;
  };

  return (
    <OutsidePressHandler
      style={styles.container}
      onOutsidePress={() => setShowTooltip(false)}>
      <View style={styles.sliderBar} />
      <TouchableOpacity
        style={[styles.dot, {left: getDotPosition()}]}
        onPress={() => setShowTooltip(prev => !prev)}>
        {showTooltip && (
          <View style={styles.tooltip}>
            <Text style={styles.tooltipTitle}>
              {NEWS.BIAS_RATING}: {position}
            </Text>
            <Text style={styles.tooltipText}>{NEWS.BIAS_RATING_DETAIL}</Text>
            <Triangle />
          </View>
        )}
      </TouchableOpacity>
    </OutsidePressHandler>
  );
};

export default BiasSlider;
