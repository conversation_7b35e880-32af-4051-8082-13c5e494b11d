import React, {useEffect, useState} from 'react';
import {View, Modal, ScrollView, TouchableOpacity} from 'react-native';
import {Text, AppButton} from '../../index';
import {Colors, Fonts} from '../../../theme';
import {CHARITY_VOTING} from '../../../constants/StringConstants';
import {styles} from './styles';
import * as Progress from 'react-native-progress';
import {size} from 'lodash';
import {
  LAUGHTER_GLOBAL,
  LIFE_GLOBAL,
  LOVE_GLOBAL,
  PURPOSE_GLOBAL,
  RESPECT_GLOBAL,
  SAFETY_GLOBAL,
} from '../../../constants/AssetSVGConstants';
import {getCharityVotingCurrentResultsRequest} from '../../../actions/CharityActions';
import {connect} from 'react-redux';
import {VotingResultItem, VotingResults} from '.././../../types';

interface CharityResultsModalProps {
  visible: boolean;
  resultsData?: VotingResults;
  onClose: () => void;
  showThankYou?: boolean;
  getCharityVotingCurrentResultsRequest: (callback: (res: any) => void) => void;
}

const CharityResultsModal: React.FC<CharityResultsModalProps> = ({
  visible,
  onClose,
  showThankYou = false,
  getCharityVotingCurrentResultsRequest,
}) => {
  const [resultsData, setResultsData] = useState<VotingResults>();

  useEffect(() => {
    if (visible) {
      getCharityVotingCurrentResultsRequest((res: any) => {
        if (res) {
          setResultsData(res);
        }
      });
    }
  }, [visible]);

  const renderCharityResult = (result: VotingResultItem) => {
    const progress = result.percentage / 100;

    return (
      <View key={result.id} style={styles.resultItem}>
        <View style={styles.percentageContainer}>
          <Text
            size={Fonts.size.xxxSmall}
            type="semi_bold"
            color={Colors.text.titleColor}>
            {result.charity.name}
          </Text>
          <Text
            size="medium"
            type="medium"
            color={Colors.text.titleColor}
            style={styles.percentageText}>
            {result.percentage.toFixed(1)}%
          </Text>
        </View>

        <View>
          <Progress.Bar
            progress={progress}
            width={null}
            height={8}
            borderRadius={4}
            color={'orange'}
            unfilledColor={Colors.lightGray1}
            borderColor="transparent"
            useNativeDriver
            style={{
              backgroundColor: '#EEEEEE',
            }}
          />
        </View>
      </View>
    );
  };

  const renderSVG = (valueType: string) => {
    switch (valueType) {
      case 'LAUGHTER':
        return <LAUGHTER_GLOBAL width={40} height={40} />;

      case 'LIFE':
        return <LIFE_GLOBAL width={40} height={40} />;

      case 'LOVE':
        return <LOVE_GLOBAL width={40} height={40} />;

      case 'PURPOSE':
        return <PURPOSE_GLOBAL width={40} height={40} />;

      case 'RESPECT':
        return <RESPECT_GLOBAL width={40} height={40} />;

      case 'SAFETY':
        return <SAFETY_GLOBAL width={40} height={40} />;

      default:
        return null;
    }
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <View style={{alignSelf: 'center'}}>
            {renderSVG(resultsData?.value)}
          </View>

          <Text
            size="large"
            type="semi_bold"
            color={Colors.text.titleColor}
            style={styles.title}>
            {showThankYou
              ? CHARITY_VOTING.RESULTS_MODAL.THANK_YOU_TITLE
              : CHARITY_VOTING.RESULTS_MODAL.TITLE}
          </Text>

          <Text
            size="small"
            type="medium"
            color={Colors.text.gray}
            style={styles.subtitle}>
            {showThankYou
              ? CHARITY_VOTING.RESULTS_MODAL.THANK_YOU_SUBTITLE
              : CHARITY_VOTING.RESULTS_MODAL.SUBTITLE}
          </Text>

          <ScrollView
            style={styles.resultsList}
            showsVerticalScrollIndicator={false}>
            {resultsData?.results.map(renderCharityResult)}
          </ScrollView>
          <Text
            style={{color: Colors.text.gray}}
            size={'xxxSmall'}
            textAlign={'center'}>
            Want to{' '}
            <Text
              size={Fonts.size.xxxSmall}
              style={{
                fontWeight: 'bold',
                color: Colors.black,
              }}>
              vote again ?{' '}
            </Text>
            Log out and log back in to drive attention to the charity you care
            about the most!
          </Text>

          <AppButton
            text={CHARITY_VOTING.RESULTS_MODAL.CLOSE}
            buttonStye={styles.closeButtonStyle}
            textColor={Colors.white}
            onPress={onClose}
            size="medium"
            type="semi_bold"
          />
        </View>
      </View>
    </Modal>
  );
};

const mapStateToProps = () => ({});

export default connect(mapStateToProps, {
  getCharityVotingCurrentResultsRequest,
})(CharityResultsModal);
