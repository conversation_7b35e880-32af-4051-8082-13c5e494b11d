import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: Metrics.screenWidth * 0.85,
    maxHeight: Metrics.screenHeight * 0.8,
    backgroundColor: Colors.white,
    borderRadius: 20,
    padding: 20,
    paddingTop: 30,
    position: 'relative',
  },
  closeButton: {
    position: 'absolute',
    top: Metrics.ratio(15),
    right: Metrics.ratio(15),
    zIndex: 1,
    padding: Metrics.ratio(8),
  },
  closeText: {
    fontSize: Metrics.ratio(18),
    color: Colors.text.gray,
    fontWeight: 'bold',
  },
  title: {
    textAlign: 'center',
    marginTop: Metrics.ratio(10),
    marginBottom: Metrics.ratio(8),
  },
  subtitle: {
    textAlign: 'center',
    marginBottom: Metrics.ratio(20),
  },
  resultsList: {
    maxHeight: Metrics.screenHeight * 0.4,
    // marginBottom: Metrics.ratio(20),
  },
  resultItem: {
    padding: Metrics.ratio(16),
    marginBottom: Metrics.ratio(16),
    backgroundColor: Colors.white,
  },
  resultHeader: {
    flexDirection: 'row',
    // justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Metrics.ratio(8),
  },
  rankContainer: {
    flex: 1,
  },
  rankText: {
    textAlign: 'left',
  },
  percentageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  percentageText: {
    fontSize: Metrics.ratio(12),
    textAlign: 'right',
  },

  voteCount: {
    textAlign: 'center',
  },
  closeButtonStyle: {
    marginHorizontal: 10,
    marginTop: Metrics.ratio(20),
    height: Metrics.ratio(44),
  },
});
