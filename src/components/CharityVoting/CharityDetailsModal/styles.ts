import {StyleSheet} from 'react-native';
import {Colors, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContainer: {
    width: Metrics.screenWidth * 0.85,
    maxHeight: Metrics.screenHeight * 0.8,
    backgroundColor: Colors.white,
    borderRadius: 20,
    padding: 20,
    paddingTop: 30,
    position: 'relative',
  },
  closeButton: {
    backgroundColor: 'transparent',
    position: 'absolute',
    top: Metrics.ratio(15),
    right: Metrics.ratio(15),
    zIndex: 1,
    padding: Metrics.ratio(8),
  },
  closeText: {
    fontSize: Metrics.ratio(18),
    color: Colors.white,
  },
  title: {
    textAlign: 'center',
    marginTop: Metrics.ratio(10),
    marginBottom: Metrics.ratio(20),
  },
  content: {
    maxHeight: Metrics.screenHeight * 0.5,
    marginBottom: Metrics.ratio(20),
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: Metrics.ratio(20),
  },
  charityIcon: {
    width: Metrics.ratio(80),
    height: Metrics.ratio(80),
    borderRadius: Metrics.ratio(40),
    backgroundColor: Colors.black,
    alignItems: 'center',
    justifyContent: 'center',
  },
  iconText: {
    textAlign: 'center',
  },
  charityName: {
    textAlign: 'center',
    marginBottom: Metrics.ratio(16),
  },
  charityDescription: {
    textAlign: 'center',
    lineHeight: Metrics.ratio(20),
    marginBottom: Metrics.ratio(16),
  },
  detailedDescription: {
    textAlign: 'left',
    lineHeight: Metrics.ratio(18),
    marginBottom: Metrics.ratio(16),
  },
  closeButtonStyle: {
    marginHorizontal: 0,
    height: Metrics.ratio(44),
  },
});
