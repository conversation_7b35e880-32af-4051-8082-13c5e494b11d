import React from 'react';
import {View, Modal, ScrollView, TouchableOpacity} from 'react-native';
import {Text, AppButton} from '../../index';
import {AppStyles, Colors, Fonts} from '../../../theme';
import {CHARITY_VOTING} from '../../../constants/StringConstants';
import {styles} from './styles';
import {getWinningCharity} from '../../../utils/charityVotingUtils';
import LinearGradient from 'react-native-linear-gradient';
import {LIFE_GLOBAL} from '../../../constants/AssetSVGConstants';
import util from '../../../util';
import {YEAR_FORMAT} from '../../../constants';
import {
  getCurrentMonthName,
  getNextMonthName,
} from '../../../utils/valueIconMapping';

interface CharityDetailsModalProps {
  visible: boolean;
  onClose: () => void;
}

const CharityDetailsModal: React.FC<CharityDetailsModalProps> = ({
  visible,
  onClose,
}) => {
  // Get the current winning charity details
  const winnerCharity = getWinningCharity();
  const nextMonth = getNextMonthName();
  const currentMonth = getCurrentMonthName();

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <LinearGradient
          colors={['#101418', '#2F1F0A']}
          start={{x: 0.5, y: 0}}
          end={{x: 0.5, y: 1}}
          style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={onClose}>
            <Text style={styles.closeText} type="bold">
              ✕
            </Text>
          </TouchableOpacity>

          <ScrollView
            style={styles.content}
            showsVerticalScrollIndicator={false}>
            {/* Charity Icon Placeholder */}
            <View style={styles.iconContainer}>
              <LIFE_GLOBAL width={40} height={40} />
            </View>
            <Text
              size="medium"
              type="semi_bold"
              color={Colors.text.white}
              style={styles.title}>
              {CHARITY_VOTING.VOTING_DAY.PREVIOUS_WINNER_TITLE}
              {currentMonth} was
            </Text>
            <Text
              size={Fonts.size.xxLarge}
              type="bold"
              color={'#F8A337'}
              style={styles.charityName}>
              {winnerCharity.name}
            </Text>
            {/* Charity Description */}
            <Text
              size={Fonts.size.xxSmall}
              type="medium"
              color={Colors.white}
              style={styles.charityDescription}>
              {winnerCharity.description}
            </Text>
            {/* Detailed Description */}
            {winnerCharity.detailedDescription && (
              <Text
                size={Fonts.size.xxSmall}
                type="medium"
                color={Colors.white}
                style={styles.detailedDescription}>
                {winnerCharity.detailedDescription}
              </Text>
            )}
            <Text
              textAlign="center"
              size={Fonts.size.xxSmall}
              type="medium"
              color={'white'}
              style={AppStyles.mTop10}>
              Next Voting date :
              <Text
                style={{color: 'yellow', fontWeight: 'bold'}}
                size={Fonts.size.xxSmall}>
                5th {nextMonth}{' '}
                {util.getFormattedDateTime(new Date(), YEAR_FORMAT)}
              </Text>
            </Text>
          </ScrollView>
        </LinearGradient>
      </View>
      {/* </LinearGradient> */}
    </Modal>
  );
};

export default CharityDetailsModal;
