import React, {useState} from 'react';
import {View, Modal, ScrollView, TouchableOpacity, Image} from 'react-native';
import {Text, ButtonView, AppButton} from '../../index';
import {Colors} from '../../../theme';
import {CHARITY_VOTING} from '../../../constants/StringConstants';
import {styles} from './styles';
import {PLACEHOLDER_IMAGE} from '../../../constants';
import {LOVE_GLOBAL} from '../../../constants/AssetSVGConstants';
import {useIsFocused} from '@react-navigation/native';

interface Charity {
  id: string;
  name: string;
  shortDescription: string;
  logoUrl: string;
}

interface CharityVotingModalProps {
  visible: boolean;
  charities: Charity[];
  onClose: () => void;
  onSubmitVote: (charityId: string) => void;
}

const CharityVotingModal: React.FC<CharityVotingModalProps> = ({
  visible,
  charities,
  onClose,
  onSubmitVote,
}) => {
  const [selectedCharityId, setSelectedCharityId] = useState<string | null>(
    null,
  );
  const [showValidationError, setShowValidationError] =
    useState<boolean>(false);

  const isfocus = useIsFocused();

  // const dispatch = useDispatch();

  const handleSubmit = () => {
    if (selectedCharityId) {
      onSubmitVote(selectedCharityId);
      setSelectedCharityId(null);
      setShowValidationError(false);
    } else {
      setShowValidationError(true);
    }
  };

  const handleClose = () => {
    setSelectedCharityId(null);
    setShowValidationError(false);
    onClose();
  };

  const renderCharityOption = (charity: Charity) => {
    const isSelected = selectedCharityId === charity.id;
    const hasValidationError = showValidationError && !selectedCharityId;

    return (
      <TouchableOpacity
        key={charity.id}
        style={[
          styles.charityOption,
          isSelected && styles.selectedOption,
          hasValidationError && styles.errorOption,
        ]}
        onPress={() => {
          setSelectedCharityId(charity.id);
          setShowValidationError(false);
        }}>
        <View style={styles.charityInfo}>
          <Image
            src={charity.logoUrl ?? PLACEHOLDER_IMAGE}
            style={{
              width: 32,
              height: 32,
              borderRadius: 16,
            }}
          />
          <View>
            <Text
              size="medium"
              type="semi_bold"
              color={Colors.text.titleColor}
              style={styles.charityName}>
              {charity.name}
            </Text>
            <Text
              numberOfLines={2}
              size="small"
              type="medium"
              color={Colors.text.gray}
              style={styles.charityDescription}>
              {charity.shortDescription}
            </Text>
          </View>
        </View>
        <View style={styles.radioContainer}>
          <View
            style={[styles.radioButton, isSelected && styles.radioSelected]}>
            {isSelected && <View style={styles.radioInner} />}
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  return (
    <Modal visible={visible} transparent animationType="fade">
      <View style={styles.overlay}>
        <View style={styles.modalContainer}>
          <TouchableOpacity style={styles.closeButton} onPress={handleClose}>
            <Text style={styles.closeText}>✕</Text>
          </TouchableOpacity>
          <View style={{alignSelf: 'center'}}>
            <LOVE_GLOBAL height={40} width={40} />
          </View>
          <Text
            size="large"
            type="semi_bold"
            color={Colors.text.titleColor}
            style={styles.title}>
            {CHARITY_VOTING.VOTING_MODAL.TITLE}
          </Text>
          <Text size="small" color={Colors.text.black} style={styles.subtitle2}>
            {CHARITY_VOTING.VOTING_MODAL.SUBTITLE_2}
          </Text>
          <Text
            size="small"
            type="bold"
            color={Colors.text.black}
            style={styles.subtitle}>
            {CHARITY_VOTING.VOTING_MODAL.SUBTITLE}
          </Text>

          <ScrollView
            style={styles.charitiesList}
            showsVerticalScrollIndicator={false}>
            {charities.map(renderCharityOption)}
          </ScrollView>
          {showValidationError && (
            <Text
              size="small"
              type="medium"
              color={Colors.red}
              style={styles.errorText}>
              {CHARITY_VOTING.VOTING_MODAL.VALIDATION_ERROR}
            </Text>
          )}

          <View style={styles.buttonContainer}>
            <ButtonView style={styles.cancelButton} onPress={handleClose}>
              <Text
                size="small"
                type="medium"
                color={Colors.text.gray}
                style={styles.cancelText}>
                {CHARITY_VOTING.VOTING_MODAL.CANCEL}
              </Text>
            </ButtonView>

            <AppButton
              text={CHARITY_VOTING.VOTING_MODAL.SUBMIT_VOTE}
              buttonStye={[
                styles.submitButton,
                !selectedCharityId && styles.disabledButton,
              ]}
              textColor={selectedCharityId ? Colors.white : Colors.text.gray}
              onPress={handleSubmit}
              size="small"
              type="semi_bold"
              //   disabled={!selectedCharityId}
            />
          </View>
        </View>
      </View>
    </Modal>
  );
};

export default CharityVotingModal;
