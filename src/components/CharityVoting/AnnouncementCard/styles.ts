import {StyleSheet} from 'react-native';
import {Colors, Fonts, Metrics} from '../../../theme';

export const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    marginHorizontal: Metrics.ratio(20),
    marginBottom: Metrics.ratio(20),
    borderRadius: Metrics.ratio(28),
    padding: Metrics.ratio(20),
    shadowColor: Colors.black,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  header: {
    alignItems: 'center',
    marginBottom: Metrics.ratio(10),
  },
  title: {
    marginTop: Metrics.ratio(10),
    textAlign: 'center',
  },
  iconContainer: {
    alignItems: 'center',
    marginBottom: Metrics.ratio(16),
  },
  content: {
    alignItems: 'center',
    //marginBottom: Metrics.ratio(16),
  },
  valueName: {
    textAlign: 'center',
    marginBottom: Metrics.ratio(8),
  },
  subtitle: {
    textAlign: 'center',
  },
  votingDate: {
    marginLeft: Metrics.ratio(10),
    textAlign: 'center',
  },
  currentWinnerLabel: {
    marginBottom: Metrics.ratio(4),
  },
  winnerName: {
    textAlign: 'center',
  },
  buttonContainer: {
    marginTop: Metrics.ratio(10),
    // backgroundColor: 'red',
    // flex: 1,
    // flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
  },
  readMoreButton: {
    flex: 1,
    alignSelf: 'center',
    marginRight: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(10),
    //  paddingHorizontal: Metrics.ratio(16),
    borderRadius: Metrics.ratio(6),
    borderWidth: 1,
    borderColor: Colors.white,
    backgroundColor: 'transparent',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Metrics.ratio(20),
  },

  voteNowButton: {
    marginTop: Metrics.ratio(10),
    // flex: 1,
    alignSelf: 'center',
    marginRight: Metrics.ratio(8),
    paddingVertical: Metrics.ratio(10),
    //  paddingHorizontal: Metrics.ratio(16),
    borderRadius: Metrics.ratio(6),
    borderWidth: 1,
    borderColor: Colors.white,
    backgroundColor: Colors.black,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Metrics.ratio(20),
  },
  readMoreText: {
    color: 'white',
    textAlign: 'center',
  },
  voteButton: {
    flex: 1,
    marginLeft: Metrics.ratio(8),
    marginHorizontal: 0,
    paddingVertical: Metrics.ratio(12),
    height: Metrics.ratio(44),
    backgroundColor: '#F8A337',
  },
  singleButtonContainer: {
    alignItems: 'center',
  },
  fullWidthButton: {
    width: '100%',
    marginHorizontal: 0,
    paddingVertical: Metrics.ratio(12),
    height: Metrics.ratio(44),
  },
  dateContainer: {
    marginTop: Metrics.ratio(15),
    borderRadius: 25,
    padding: Metrics.ratio(10),
    flexDirection: 'row',
    alignItems: 'center',
    // backgroundColor: 'red',
    borderWidth: 2,
    borderColor: '#F8A337',
  },
  highlight: {
    fontSize: Fonts.size.normal,
    color: '#FFA500',
  },

  description: {
    fontSize: Fonts.size.small,
    textAlign: 'center',
    lineHeight: 20,
  },
  thankYouText: {
    fontSize: Fonts.size.normal,
    fontWeight: 'bold',
    color: '#333',
    textAlign: 'center',
    marginTop: Metrics.ratio(10),
  },
  imagePadding: {
    paddingLeft: Metrics.ratio(6),
  },
});
