import React, {useEffect, useState} from 'react';
import {View} from 'react-native';
import {Text, ButtonView, AppButton} from '../../index';
import {AppStyles, Colors, Fonts, Metrics} from '../../../theme';
import {
  CHARITY_VOTING,
  IMPRINT_SCORES,
  VOTING,
} from '../../../constants/StringConstants';
import {styles} from './styles';
import {
  getVotingFlowType,
  getCurrentWinner,
  type VotingFlowType,
} from '../../../utils/charityVotingUtils';
import {
  getCurrentMonthValue,
  getPreviousMonthValue,
  getValueIcon,
  getValueName,
  getCurrentMonthName,
  getNextMonthName,
} from '../../../utils/valueIconMapping';
import {LinearGradient} from 'react-native-linear-gradient';
import {stubArray} from 'lodash';
import {
  CALENDAR,
  LAUGHTER,
  LIFE,
  LIFE_GLOBAL,
  LOVE,
  LOVE_GLOBAL,
  LOVE_REACTION,
  PURPOSE,
  RESPECT,
  SAFETY,
  TICK_GREEN,
} from '../../../constants/AssetSVGConstants';
import util from '../../../util';
import {YEAR_FORMAT} from '../../../constants';
import {
  getCharityResultRequest,
  getCharityVotingCurrentResultsRequest,
} from '../../../actions/CharityActions';
import {connect} from 'react-redux';

interface CharityAnnouncementCardProps {
  onReadMorePress: () => void;
  onVoteNowPress: () => void;
  onCharityDetailsPress?: (item: any) => void;
  isAlreadyVoted: boolean;
  getCharityResultRequest: (callback: (res: any) => void) => void;
}

const CharityAnnouncementCard: React.FC<CharityAnnouncementCardProps> = ({
  onReadMorePress,
  onVoteNowPress,
  onCharityDetailsPress,
  getCharityResultRequest,
  isAlreadyVoted = false,
}) => {
  const flowType = getVotingFlowType();

  const [winnerCharity, setWinnerCharity] = useState({});

  if (flowType === 'NONE') {
    return null;
  }

  useEffect(() => {
    getCharityResultRequest((res: any) => {
      if (res) {
        setWinnerCharity(res);
        console.log('RES', res[0].value);
      }
    });
  }, []);

  const renderAnnouncementContent = () => {
    const currentValue = getCurrentMonthValue();
    const ValueIcon = getValueIcon(currentValue);
    const valueName = getValueName(currentValue);
    const nextMonth = getNextMonthName();

    return (
      <>
        <View style={styles.header}>
          <LOVE_GLOBAL />
          <Text
            size={Fonts.size.xSmall}
            type="bold"
            color={Colors.text.black}
            style={styles.title}>
            {CHARITY_VOTING.ANNOUNCEMENT.TITLE}
          </Text>
        </View>

        {/* <View style={styles.iconContainer}>
          <ValueIcon width={60} height={60} />
        </View> */}

        <View style={styles.content}>
          {/* <Text
            size="large"
            type="semi_bold"
            color={Colors.text.titleColor}
            style={styles.valueName}>
            {valueName}
          </Text> */}
          <Text
            size={Fonts.size.xxSmall}
            type="regular"
            color={'#393C4C'}
            style={styles.subtitle}>
            {CHARITY_VOTING.ANNOUNCEMENT.SUBTITLE}
          </Text>
          <View style={styles.dateContainer}>
            <CALENDAR />
            <Text
              size="small"
              type="semi_bold"
              color={Colors.text.titleColor}
              style={styles.votingDate}>
              {CHARITY_VOTING.ANNOUNCEMENT.VOTING_STARTS_ON} {nextMonth}
            </Text>
          </View>
        </View>
      </>
    );
  };
  const VotingText = (valueName: string) => {
    return (
      <Text
        color="black"
        size={Fonts.size.xxxSmall}
        type="regular"
        textAlign="center">
        Caste your vote for the charity associated with{' '}
        <Text
          style={{fontWeight: 'bold'}}
          color="black"
          size={Fonts.size.xxxSmall}>
          {valueName}
        </Text>{' '}
        our viral value for {getCurrentMonthName()}
      </Text>
    );
  };

  const renderSVG = (valueType: string) => {
    switch (valueType) {
      case IMPRINT_SCORES.LAUGHTER.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LAUGHTER width={40} height={26} />
          </View>
        );
      case IMPRINT_SCORES.LIFE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LIFE width={40} height={40} />
          </View>
        );
      case IMPRINT_SCORES.LOVE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <LOVE width={40} height={40} />
          </View>
        );
      case IMPRINT_SCORES.PURPOSE.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <PURPOSE width={40} height={40} />
          </View>
        );
      case IMPRINT_SCORES.RESPECT.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <RESPECT width={40} height={40} />
          </View>
        );
      case IMPRINT_SCORES.SAFETY.toLocaleUpperCase():
        return (
          <View style={styles.imagePadding}>
            <SAFETY width={40} height={40} />
          </View>
        );
      default:
        return <></>;
    }
  };

  const renderVotingDayContent = () => {
    return (
      <View style={styles.container}>
        {!isAlreadyVoted ? (
          <>
            <View style={styles.header}>
              <LOVE_GLOBAL height={36} width={36} />
              <Text
                size={Fonts.size.large}
                type="bold"
                color={Colors.text.black}
                style={styles.title}>
                {CHARITY_VOTING.VOTING_MODAL.TITLE}
              </Text>
            </View>
            {VotingText('LOVE')}
          </>
        ) : (
          <>
            <View style={AppStyles.alignItemsCenter}>
              <TICK_GREEN height={25} width={25} />
            </View>
            <Text style={styles.thankYouText}>
              {VOTING.THANKS}

              <Text style={styles.highlight}>{VOTING.VALUE}</Text>
            </Text>

            <Text
              color="#393C4C"
              size={Fonts.size.xxSmall}
              style={[AppStyles.mTop10, AppStyles.mBottom10]}>
              Want to{' '}
              <Text
                style={{fontWeight: 'bold', color: Colors.black}}
                size={Fonts.size.xSmall}>
                vote again ?
              </Text>{' '}
              Log out and log back in to drive attention to the charity you care
              about the most!
            </Text>
          </>
        )}

        <View style={{flex: 1}}>
          <ButtonView
            style={styles.voteNowButton}
            onPress={isAlreadyVoted ? onReadMorePress : onVoteNowPress}>
            <Text
              size="small"
              type="medium"
              color={'white'}
              style={styles.readMoreText}>
              {isAlreadyVoted
                ? CHARITY_VOTING.VOTING_DAY.VIEW_RESULT
                : CHARITY_VOTING.VOTING_DAY.VOTE_NOW}
            </Text>
          </ButtonView>
        </View>
      </View>
    );
  };

  const renderResultsAnnouncementContent = () => {
    const currentValue = getCurrentMonthValue();
    const ValueIcon = getValueIcon(currentValue);
    const valueName = getValueName(currentValue);
    const nextMonth = getNextMonthName();
    const currentMonth = getCurrentMonthName();

    return (
      <>
        <View style={styles.header}>
          {renderSVG(winnerCharity[0]?.value)}
          {/* <LIFE_GLOBAL height={36} width={36} /> */}
          <Text
            size="medium"
            type="semi_bold"
            color={Colors.text.white}
            style={styles.title}>
            {CHARITY_VOTING.VOTING_DAY.PREVIOUS_WINNER_TITLE}
            {currentMonth} was
          </Text>
        </View>

        <View style={styles.content}>
          <Text
            size={Fonts.size.xxLarge}
            type="bold"
            color={'#F8A337'}
            style={styles.winnerName}>
            {winnerCharity[0]?.winner?.name}!
          </Text>
        </View>

        <View style={styles.buttonContainer}>
          <ButtonView
            style={styles.readMoreButton}
            onPress={onCharityDetailsPress}>
            <Text
              size="small"
              type="medium"
              color={'white'}
              style={styles.readMoreText}>
              {CHARITY_VOTING.VOTING_DAY.READ_MORE}
            </Text>
          </ButtonView>
          <Text
            size={Fonts.size.xxSmall}
            type="medium"
            color={'white'}
            style={AppStyles.mTop10}>
            Next Voting date :
            <Text
              style={{color: 'yellow', fontWeight: 'bold'}}
              size={Fonts.size.xxSmall}>
              5th {nextMonth}{' '}
              {util.getFormattedDateTime(new Date(), YEAR_FORMAT)}
            </Text>
          </Text>
        </View>
      </>
    );
  };

  return (
    <>
      {flowType === 'ANNOUNCEMENT' && (
        <View style={styles.container}>{renderAnnouncementContent()}</View>
      )}

      {flowType === 'RESULTS_ANNOUNCEMENT' && (
        <LinearGradient
          colors={['#101418', '#2F1F0A']}
          start={{x: 0.5, y: 0}}
          end={{x: 0.5, y: 1}}
          style={styles.container}>
          {renderResultsAnnouncementContent()}
        </LinearGradient>
      )}

      {flowType === 'VOTING_DAY' && renderVotingDayContent()}
    </>
  );
};

const mapStateToProps = () => ({});

export default connect(mapStateToProps, {
  getCharityResultRequest,
})(CharityAnnouncementCard);
