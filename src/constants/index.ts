import moment from 'moment';

import {
  COMMON,
  ENTERTAINMENTS_LIST,
  FRIENDS,
  IMPRINT_SCORES,
  IMPRINT_SCORES_DISPLAY,
  NEWS,
  PROFILE_OPTIONS_LIST,
} from './StringConstants';
import {
  LAUGHTER_GLOBAL,
  LAUGHTER_PERSONAL,
  LAUGHTER_STONE,
  LIFE,
  LIFE_GLOBAL,
  LIFE_PERSONAL,
  LIFE_STONE,
  LOVE_GLOBAL,
  LOVE_PERSONAL,
  LOVE_STONE,
  PURPOSE_GLOBAL,
  PURPOSE_PERSONAL,
  PURPOSE_STONE,
  RESPECT_GLOBAL,
  RESPECT_PERSONAL,
  RESPECT_STONE,
  SAFETY,
  SAFETY_GLOBAL,
  SAFETY_PERSONAL,
  SAFETY_STONE,
  LOVE,
  RESPECT,
  LAUGHTER,
  PURPOSE,
} from './AssetSVGConstants';
import {Colors} from '../theme';
import Routes from './RouteConstants';

export const PASSWORD_MIN_LENGTH = 6;
export const PASSWORD_MAX_LENGTH = 20;
// export const TIME_ZONE = (-1 * new Date().getTimezoneOffset()) / 60;
export const APP_URL = '';
export const APP_DOMAIN = '';
export const QUERY_LIMIT = 10;
export const SAGA_ALERT_TIMEOUT = 500;
export const POST_VIEW_TIMEOUT = 2000;
export const IMAGE_QUALITY = 1;
export const IMAGE_MAX_WIDTH = 720;
export const IMAGE_MAX_HEIGHT = 480;
export const IMAGE_COMPRESS_MAX_WIDTH = 720;
export const IMAGE_COMPRESS_MAX_HEIGHT = 480;
export const VERSES_OF_THE_DAY_LIMIT = 10;
export const IMAGE_COMPRESS_FORMAT = 'JPEG';
export const ANDROID_NOTI_CHANNEL = 'VeteranAppChanel';
export const TOAST_VISIBILITY_TIMEOUT = 2000;
export const SUPPORT_CENTER_LINK =
  'https://support.imprint.live/hc/en-us/articles/34086260519569-Next-of-Kin-Policy';

export const PRIVACY_POLICY_LINK = 'https://sigma.imprint.live/privacy-policy';
export const PRIVACY_POLICY_LINK_LIVE =
  'https://app.imprint.live/privacy-policy';
export const TERMS_AND_CONDITIONS_LINK =
  'https://sigma.imprint.live/terms-of-service';
export const TERMS_AND_CONDITIONS_LINK_LIVE =
  'https://app.imprint.live/terms-of-service';
export const DISCARD_WARNING: String = 'Are You sure to discard';

// date time formats
export const DATE_FORMAT1 = 'dddd, DD MMMM, YYYY';
export const DATE_FORMAT2 = 'YYYY MMMM DD';
export const DATE_FORMAT3 = 'YYYY-MM-DD';
export const TIME_FORMAT1 = 'hh:mm A';
export const TIME_FORMAT2 = 'HH:mm ';
export const DATE_FORMAT_8 = ' DD MMMM, YYYY';
export const YEAR_FORMAT = 'YYYY';

export const DATE_FORMAT_TIME1 = 'Do | MMM | HH';
export const DATE_FORMAT4 = 'dddd, DD MMMM YYYY';
export const DATE_FORMAT5 = 'MMM DD, YYYY';
export const DATE_FORMAT6 = 'MMM-DD';
export const DATE_FORMAT_7 = 'MMM DD, YYYY, hh:mm A';

export const LATITUDE_DELTA = 0.0922;
export const LONGITUDE_DELTA = 0.0421;

export const PLACEHOLDER_IMAGE =
  'https://cdn.pixabay.com/photo/2016/08/08/09/17/avatar-1577909_960_720.png';
// Message types
export const MESSAGE_TYPES = {
  INFO: 'info',
  ERROR: 'error',
  SUCCESS: 'success',
};
export const ABOUT_IMPRINT_PAGE = 'https://delta.imprint.live/about-imprint';

// File Types
export const FILE_TYPES = {VIDEO: 'video', IMAGE: 'image', AUDIO: 'audi'};

export const MENTIONS_REGEX =
  /(https?:\/\/|www\.)[-a-zA-Z0-9@:%._\+~#=]{1,256}\.(xn--)?[a-z0-9-]{2,20}\b([-a-zA-Z0-9@:%_\+\[\],.~#?&\/=]*[-a-zA-Z0-9@:%_\+\]~#?&\/=])*/gi;

export const IMPRINT_SCORES_LIST = [
  {
    id: 1,
    displayScoreName: IMPRINT_SCORES_DISPLAY.LIFE,
    serverScoreName: IMPRINT_SCORES.LIFE,
    personalImage: LIFE_PERSONAL,
    globalImage: LIFE_GLOBAL,
  },
  {
    id: 2,
    displayScoreName: IMPRINT_SCORES_DISPLAY.LOVE,
    serverScoreName: IMPRINT_SCORES.LOVE,
    personalImage: LOVE_PERSONAL,
    globalImage: LOVE_GLOBAL,
  },
  {
    id: 3,
    displayScoreName: IMPRINT_SCORES_DISPLAY.RESPECT,
    serverScoreName: IMPRINT_SCORES.RESPECT,
    personalImage: RESPECT_PERSONAL,
    globalImage: RESPECT_GLOBAL,
  },
  {
    id: 4,
    displayScoreName: IMPRINT_SCORES_DISPLAY.SAFETY,
    serverScoreName: IMPRINT_SCORES.SAFETY,
    personalImage: SAFETY_PERSONAL,
    globalImage: SAFETY_GLOBAL,
  },

  {
    id: 5,
    displayScoreName: IMPRINT_SCORES_DISPLAY.LAUGHTER,
    serverScoreName: IMPRINT_SCORES.LAUGHTER,
    personalImage: LAUGHTER_PERSONAL,
    globalImage: LAUGHTER_GLOBAL,
  },

  {
    id: 6,
    displayScoreName: IMPRINT_SCORES_DISPLAY.PURPOSE,
    serverScoreName: IMPRINT_SCORES.PURPOSE,
    personalImage: PURPOSE_PERSONAL,
    globalImage: PURPOSE_GLOBAL,
  },
];

export const QUIZ_ITEM = {
  id: 7,
  displayScoreName: 'All',
  serverScoreName: 'all',
  personalImage: PURPOSE_PERSONAL,
  globalImage: PURPOSE_GLOBAL,
};

export const PROFILE_OPTIONS = [
  {
    id: 1,
    name: PROFILE_OPTIONS_LIST.POSTS,
  },
  {
    id: 2,
    name: PROFILE_OPTIONS_LIST.ABOUT,
  },
  {
    id: 3,
    name: PROFILE_OPTIONS_LIST.FRIENDS,
  },
  {
    id: 4,
    name: PROFILE_OPTIONS_LIST.MORE,
  },
];

export const IMPRINT_MEDIA_TYPES_MAP = {
  document: 'Document',
  video: 'Video',
  image: 'Image',
  text: 'Document',
  map: 'Map',
  audio: 'Audio',
};

export const STONES_DATA = [
  {
    id: 0,
    text: 'love',
    image: LOVE_STONE,
  },
  {
    id: 1,
    text: 'life',
    image: LIFE_STONE,
  },
  {
    id: 2,
    text: 'respect',
    image: RESPECT_STONE,
  },
  {
    id: 4,
    text: 'purpose',
    image: PURPOSE_STONE,
  },
  {
    id: 5,
    text: 'safety',
    image: SAFETY_STONE,
  },
  {
    id: 6,
    text: 'laughter',
    image: LAUGHTER_STONE,
  },
];

export const DEFAULT_PADDING = {top: 80, right: 120, bottom: 80, left: 120};

export const KIN_RELATION = [
  {label: 'Friends', value: 'Friends'},
  {label: 'Family', value: 'Family'},
  {label: 'Colleague', value: 'Colleague'},
];

export const CHAT_MORE_FOR_OWN_MESSAGES = [
  'Delete for me',
  'Delete for everyone',
  'Cancel',
];

export const CHAT_MORE_FOR_OTHER_MESSAGES = [
  'Delete',
  'Report message',
  'Cancel',
];
export const svgComponents: {[key: string]: React.ElementType} = {
  life: LIFE,
  safety: SAFETY,
  love: LOVE,
  respect: RESPECT,
  purpose: PURPOSE,
  laughter: LAUGHTER,
};
// export const subscriptionData = [
//   'For All Users',
//   'Access Everything!',
//   'Post text imprints only',
//   '20% of your subscription goes to charity!',
// ];

export const getItems = (planName: string) => [
  'For All Users',
  'Access Everything!',
  planName === 'Gold'
    ? 'Post text and media imprints only'
    : 'Post text imprints only',

  '20% of your subscription goes to charity!',
];

export const colorMap = {
  Gold: Colors.SubscriptionsItem.goldColor,
  Silver: Colors.SubscriptionsItem.silverColor,
};

export const ENTERTAINMENTS_OPTIONS = [
  {
    id: 1,
    name: ENTERTAINMENTS_LIST.GOOD_DEEDS,
    serverName: 'GOOD_DEED',
  },
  {
    id: 2,
    name: ENTERTAINMENTS_LIST.QUOTES,
    serverName: 'QUOTE',
  },
  {
    id: 3,
    name: ENTERTAINMENTS_LIST.JOKES,
    serverName: 'JOKE',
  },
  {
    id: 4,
    name: ENTERTAINMENTS_LIST.QUIZ,
    serverName: 'QUIZ',
  },
  {
    id: 5,
    name: ENTERTAINMENTS_LIST.GAMES,
    serverName: 'Game',
  },
];

export const NEWS_TABS = [
  {
    id: 1,
    name: NEWS.GENERAL_NEWS,
  },
  {
    id: 2,
    name: NEWS.MY_NEWS,
  },
];

export const GIF_IMAGES = [
  {
    id: 1,
    name: 'dino',
    attachmentUrl: '/GIFs/dino.gif',
    imagePath: require('../assets/gif/dino.gif'),
  },
  {
    id: 2,
    name: 'leaves',
    attachmentUrl: '/GIFs/leaves.gif',
    imagePath: require('../assets/gif/leaves.gif'),
  },
  {
    id: 3,
    name: 'tiger',
    attachmentUrl: '/GIFs/tiger.gif',
    imagePath: require('../assets/gif/tiger.gif'),
  },
];
export const gifMapping: any = {
  '/GIFs/dino.gif': require('../assets/gif/dino.gif'),
  '/GIFs/leaves.gif': require('../assets/gif/leaves.gif'),
  '/GIFs/tiger.gif': require('../assets/gif/tiger.gif'),
};
export const AskRudiOptions = [
  NEWS.TELL_ME_MORE,
  NEWS.EXPLAIN,
  NEWS.SHOW_SOURCES,
  COMMON.CANCEL,
];
export const BADGE_TYPES = [
  'VALUE_BADGE_ASSIGNED_LAUGHTER',
  'VALUE_BADGE_ASSIGNED_SAFETY',
  'VALUE_BADGE_ASSIGNED_PURPOSE',
  'VALUE_BADGE_ASSIGNED_RESPECT',
  'VALUE_BADGE_ASSIGNED_SAD',
  'VALUE_BADGE_ASSIGNED_LOVE',
  'VALUE_BADGE_ASSIGNED_LIFE',
];

export const VALID_TAGS = ['p', 'div', 'b', 'i', 'u', 'span', 'strong', 'em'];

export const ZENDESK_CODE =
  'olZQJ5EQytUpFdfEui7q09zLYLmUgmSBm8x-KNhcpZ-XAzFuDP8EVg%3D%3D';

export const return_to = 'https://support.imprint.live/hc/en-us';

export const allowedRoutesForFreeUsers = [
  Routes.HOME_STACK,
  Routes.PROFILE_STACK,
];

export const PERMISSIONS = {
  BOOKMARK_READ: 'bookmark:read',
  BOOKMARK_CREATE: 'bookmark:create',
  BOOKMARK_EDIT: 'bookmark:edit',
  CHECKIN_READ: 'checkIn:read',
  CHECKIN_CREATE: 'checkIn:create',
  CHECKIN_EDIT: 'checkIn:edit',
  CHATBOT_INTERACT: 'chatbot:interact',
  CONVERSATION_READ: 'conversation:read',
  CONVERSATION_CREATE: 'conversation:create',
  CONVERSATION_EDIT: 'conversation:edit',
  IMPRINT_UPLOAD_IMAGE: 'imprint:upload-image',
  IMPRINT_UPLOAD_VIDEO: 'imprint:upload-video',
  IMPRINT_UPLOAD_SPEECH: 'imprint:upload-speech',
  VALUEPROFILE_READ: 'valueProfile:read',
  IMPRINT_UPLOAD_ARTICLE: 'imprint:upload-article',
  IMPRINT_UPLOAD_MEDIA: 'imprint:upload-media',
  MESSAGES_READ: 'messages:read',
  MESSAGES_CREATE: 'messages:create',
};

// ✅ ProPlans - Only hardcoded "Coming Soon" plans that are disabled
export const ProPlans = [
  {
    title: 'Pro',
    name: 'Pro',
    price: 7.99,
    currency: '£',
    billingCycle: 'monthly',
    label: '£3.56*/month',
    description:
      'Amplify your impact by promoting and driving awareness for the causes you care about.',
    charity: 2,
    productId: 'pro_plan', // Placeholder product ID
    isAnnual: false,
    isComingSoon: true, // ✅ Always disabled - Coming Soon
  },
  {
    title: 'Pro Max',
    name: 'Pro Max',
    price: 12.99,
    currency: '£',
    billingCycle: 'monthly',
    label: '£5.71*/month',
    description:
      'Inspire others, and represent key causes within the community.',
    charity: 5,
    productId: 'pro_max_plan', // Placeholder product ID
    isAnnual: false,
    isComingSoon: true, // ✅ Always disabled - Coming Soon
  },
  {
    title: 'Pro Max Plus',
    name: 'Pro Max Plus',
    price: 22.99,
    currency: '£',
    billingCycle: 'monthly',
    label: '£5.71*/month',
    description:
      'Create and lead transformative movements towards greater causes.',
    charity: 10,
    productId: 'pro_max_plus_plan', // Placeholder product ID
    isAnnual: false,
    isComingSoon: true, // ✅ Always disabled - Coming Soon
  },
];

export const ProPlansYearly = [
  {
    title: 'Pro',
    name: 'Pro',
    price: 92.99,
    currency: '£',
    billingCycle: 'annual',
    label: '£13.56*/year',
    description:
      'Amplify your impact by promoting and driving awareness for the causes you care about.',
    charity: 2,
    productId: 'pro_plan_annual', // Placeholder product ID
    isAnnual: false,
    isComingSoon: true, // ✅ Always disabled - Coming Soon
  },
  {
    title: 'Pro Max',
    name: 'Pro Max',
    price: 149.99,
    currency: '£',
    billingCycle: 'annual',
    label: '£5.71*/year',
    description:
      'Inspire others, and represent key causes within the community.',
    charity: 5,
    productId: 'pro_max_plan_annual', // Placeholder product ID
    isAnnual: false,
    isComingSoon: true, // ✅ Always disabled - Coming Soon
  },
  {
    title: 'Pro Max Plus',
    name: 'Pro Max Plus',
    price: 269.99,
    currency: '£',
    billingCycle: 'annual',
    label: '£5.71*/year',
    description:
      'Create and lead transformative movements towards greater causes.',
    charity: 10,
    productId: 'pro_max_plus_plan_annual', // Placeholder product ID
    isAnnual: false,
    isComingSoon: true, // ✅ Always disabled - Coming Soon
  },
];
