import {call, fork, take} from 'redux-saga/effects';
import {
  CHARITY_VOTING_OPTIONS,
  GET_CHARITY_RESULT,
  GET_CHARITY_VOTING_CURRENT_RESULTS,
  VOTE_FOR_CHARITY,
} from '../actions/ActionTypes';
import {
  GET_CHARITY_RESULT as GET_CHARITY_RESULT_URL,
  GET_CHARITY_VOTING_CURRENT_RESULTS as GET_CHARITY_VOTING_CURRENT_RESULTS_URL,
  VOTE_FOR_CHARITY as VOTE_FOR_CHARITY_URL,
  CHARITY_VOTING_OPTIONS as CHARITY_VOTING_OPTIONS_URL,
  callRequest,
} from '../config/WebService';
import ApiSauce from '../services/ApiSauce';
import Util from '../util';
import {showToastMsg} from '../components/Alert';
import _ from 'lodash';

function alert(message, type = 'error') {
  showToastMsg(message);
}

function* getCharityVotingList() {
  while (true) {
    const {responseCallback} = yield take(CHARITY_VOTING_OPTIONS.REQUEST);
    try {
      const response = yield call(
        callRequest,
        CHARITY_VOTING_OPTIONS_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* voteForCharity() {
  while (true) {
    const {payload, responseCallback} = yield take(VOTE_FOR_CHARITY.REQUEST);
    try {
      const response = yield call(
        callRequest,
        VOTE_FOR_CHARITY_URL,
        payload,
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getCurrentCharityResults() {
  while (true) {
    const {responseCallback} = yield take(
      GET_CHARITY_VOTING_CURRENT_RESULTS.REQUEST,
    );
    try {
      const response = yield call(
        callRequest,
        GET_CHARITY_VOTING_CURRENT_RESULTS_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

function* getCharityResult() {
  while (true) {
    const {responseCallback} = yield take(GET_CHARITY_RESULT.REQUEST);
    try {
      const response = yield call(
        callRequest,
        GET_CHARITY_RESULT_URL,
        {},
        '',
        {},
        ApiSauce,
      );
      if (response) {
        if (responseCallback) responseCallback(response, null);
      } else {
        alert('Something went wrong');
      }
    } catch (err) {
      if (responseCallback) responseCallback(null, err);
      alert(Util.getErrorText(err.message));
    }
  }
}

export default function* root() {
  yield fork(getCharityVotingList);
  yield fork(voteForCharity);
  yield fork(getCurrentCharityResults);
  yield fork(getCharityResult);
}
