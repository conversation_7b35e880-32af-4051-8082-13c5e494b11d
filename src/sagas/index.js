import {fork} from 'redux-saga/effects';
import user from './user';
import timeline from './timeline';
import post from './post';
import followers from './followers';
import profile from './profile';
import chat from './chat';
import analytics from './analytics';
import notification from './notification';
import rudi from './rudi';
import entertainments from './entertainments';
import search from './search';
import charity from './charity';

export default function* root() {
  yield fork(user);
  yield fork(timeline);
  yield fork(post);
  yield fork(followers);
  yield fork(profile);
  yield fork(chat);
  yield fork(analytics);
  yield fork(notification);
  yield fork(rudi);
  yield fork(entertainments);
  yield fork(search);
  yield fork(charity);
}
